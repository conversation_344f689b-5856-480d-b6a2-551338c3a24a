'use client'
import { AppSidebar, type NavItem } from '@/components/custom/app-sidebar'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { LayoutDashboard, ScanEye } from 'lucide-react'
// Menu items.
const items: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/admin/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Guidance Templates',
    url: '/admin/guidance-templates',
    icon: Scan<PERSON>ye,
  },
  {
    title: 'Premises',
    url: '/admin/premises',
    icon: Scan<PERSON><PERSON>,
  },
  {
    title: 'Incidents',
    url: '/incidents',
    icon: <PERSON>an<PERSON><PERSON>,
  },
  {
    title: 'Cameras',
    url: '/admin/cameras',
    icon: <PERSON>an<PERSON><PERSON>,
  },
  {
    title: 'Guards',
    url: '/admin/guards',
    icon: Scan<PERSON><PERSON>,
  },
  {
    title: 'Settings',
    url: '/admin/settings',
    icon: ScanEye,
  },
]
function Sidebar() {
  return (
    <>
      <AppSidebar items={items} />
      <SidebarTrigger className="top-0 right-[-25px] absolute " />
    </>
  )
}

export default Sidebar
